<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <title>Documents</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <!-- debug begin -->
    <link rel="stylesheet/less" type="text/css" href="resources/less/application.less" />
    <!-- debug end -->

    <!-- splash -->

    <style type="text/css">
        .loadmask {
            left: 0;
            top: 0;
            position: absolute;
            height: 100%;
            width: 100%;
            overflow: hidden;
            border: none;
            background-color: #f4f4f4;
            z-index: 1000;
        }

        .loadmask > .brendpanel {
            width: 100%;
            position: absolute;
            height: 28px;
            background-color: #F7F7F7;
            -webkit-box-shadow: inset 0 -1px 0 #dbdbdb, inset 0 1px 0 #FAFAFA;
            box-shadow: inset 0 -1px 0 #dbdbdb, inset 0 1px 0 #FAFAFA;
        }

        .loadmask > .brendpanel > div {
            display: flex;
            align-items: center;
            height: 100%;
        }

        .loadmask > .brendpanel .doc-title {
            flex-grow: 1;
        }

        .loadmask > .brendpanel .circle {
            vertical-align: middle;
            width: 24px;
            height: 24px;
            border-radius: 12px;
            margin: 4px 10px;
            background: rgba(255, 255, 255, 0.2);
        }

        .loadmask > .placeholder {
            background: #f5f5f5;
            width: 100%;
            height: 100%;
            padding-top: 48px;
        }

        .loadmask > .placeholder .slide-h {
            display: flex;
            flex-direction: column;
            justify-content: center;
            flex-grow: 1;
            width: 90%;
            margin: 0 auto;
        }
        .loadmask > .placeholder .slide-v {
            display: flex;
            position: relative;
            flex-direction: column;
            padding-bottom: 90%;
        }

        .loadmask > .placeholder .slide-container {
            position: absolute;
            height: 100%;
            width: 100%;
            background: #fbfbfb;
            border: 1px solid #dfdfdf;

            -webkit-animation: flickerAnimation 2s infinite ease-in-out;
            -moz-animation: flickerAnimation 2s infinite ease-in-out;
            -o-animation: flickerAnimation 2s infinite ease-in-out;
            animation: flickerAnimation 2s infinite ease-in-out;
        }

        @keyframes flickerAnimation {
            0%   { opacity:1; }
            50%  { opacity:0.3; }
            100% { opacity:1; }
        }
        @-o-keyframes flickerAnimation{
            0%   { opacity:1; }
            50%  { opacity:0.3; }
            100% { opacity:1; }
        }
        @-moz-keyframes flickerAnimation{
            0%   { opacity:1; }
            50%  { opacity:0.3; }
            100% { opacity:1; }
        }
        @-webkit-keyframes flickerAnimation{
            0%   { opacity:1; }
            50%  { opacity:0.3; }
            100% { opacity:1; }
        }

        .loadmask.none-animation > .placeholder .slide-container {
            animation: none;
        }
    </style>

    <!--[if lt IE 9]>
      <script src="//cdnjs.cloudflare.com/ajax/libs/html5shiv/3.6.1/html5shiv.js"></script>
    <![endif]-->
      <script>
          if(/MSIE \d|Trident.*rv:/.test(navigator.userAgent)) {
              document.write('<script src="../../common/main/lib/util/fix-ie-compat.js"><\/script>');
              document.write('<script src="../../../../sdkjs/vendor/string.js"><\/script>');
          }
      </script>
  </head>

  <body class="embed-body">
    <script type="text/javascript" src="../../common/embed/lib/util/htmlutils.js"></script>
      <div id="loading-mask" class="loadmask">
          <div class="brendpanel">
              <div>
                  <div class="doc-title"></div>
                  <div class="circle"></div>
              </div>
          </div>
          <div class="placeholder">
              <div class="slide-h">
                  <div class="slide-v">
                      <div class="slide-container"></div>
                  </div>
              </div>
          </div>
      </div>
      <!-- debug begin -->
      <script type="text/javascript">var less=less||{};less.env='development';</script>
      <script src="../../../vendor/less/dist/less.js" type="text/javascript"></script>
      <!-- debug end -->

      <script>
          var  userAgent = navigator.userAgent.toLowerCase(),
               check = function(regex){ return regex.test(userAgent); };
          if (!check(/opera/) && (check(/msie/) || check(/trident/))) {
               var m = /msie (\d+\.\d+)/.exec(userAgent);
               if (m && parseFloat(m[1]) < 10.0) {
                   document.write(
                       '<div id="id-error-mask" class="errormask">',
                           '<div class="error-body" align="center">',
                               '<div id="id-error-mask-title" class="title">Your browser is not supported.</div>',
                               '<div id="id-error-mask-text">Sorry, ONLYOFFICE Document is currently only supported in the latest versions of the Chrome, Firefox, Safari or Internet Explorer web browsers.</div>',
                           '</div>',
                       '</div>'
                   );
               }
          }

          function getUrlParams() {
              var e,
                  a = /\+/g,  // Regex for replacing addition symbol with a space
                  r = /([^&=]+)=?([^&]*)/g,
                  d = function (s) { return decodeURIComponent(s.replace(a, " ")); },
                  q = window.location.search.substring(1),
                  urlParams = {};

              while (e = r.exec(q))
                  urlParams[d(e[1])] = d(e[2]);

              return urlParams;
          }

          function encodeUrlParam(str) {
              return str.replace(/"/g, '&quot;')
                      .replace(/'/g, '&#39;')
                      .replace(/</g, '&lt;')
                      .replace(/>/g, '&gt;');
          }

          var params = getUrlParams(),
              lang = (params["lang"] || 'en').split(/[\-\_]/)[0];

          window.frameEditorId = params["frameEditorId"];
          window.parentOrigin = params["parentOrigin"];
      </script>

      <div class="viewer">
          <div id="editor_sdk" class="sdk-view" style="overflow: hidden;" tabindex="-1"></div>
          <div id="pages-container" class="pages-list-container">
              <div class="pages-list-buttons">
                  <button id="pages-list-button-prev" class="control-btn svg-icon search-arrow-left" disabled>
                  </button>
                  <button id="pages-list-button-next" class="control-btn svg-icon search-arrow-right" disabled>
                  </button>
              </div>
              <ul id="id-pages" class="pages-list"></ul>
          </div>
      </div>

      <div class="overlay-controls" style="margin-left: -32px">
        <ul class="left">
            <li id="id-btn-zoom-in"><button class="overlay svg-icon zoom-up"></button></li>
            <li id="id-btn-zoom-out"><button class="overlay svg-icon zoom-down"></button></li>
        </ul>
      </div>

      <div class="toolbar" id="toolbar">
          <div class="group left">
              <div class="margin-right-large"><a id="header-logo" class="brand-logo" href="http://www.onlyoffice.com/" target="_blank"></a></div>
          </div>
          <div class="group center">
              <span id="title-doc-name"></span>
          </div>
          <div class="group right">
              <div id="box-tools" class="dropdown">
                  <button class="control-btn svg-icon more-vertical"></button>
              </div>
              <button id="id-btn-close-editor" class="control-btn svg-icon search-close hidden"></button>
          </div>
      </div>

      <div class="error modal fade" id="id-critical-error-dialog" tabindex="-1" role="dialog">
          <div class="modal-dialog">
              <div class="modal-content">
                  <div class="modal-header">
                      <h4 id="id-critical-error-title"></h4>
                  </div>
                  <div class="modal-body">
                      <p id="id-critical-error-message"></p>
                  </div>
                  <div class="modal-footer">
                      <button id="id-critical-error-close" class="btn btn-sm" data-dismiss="modal" aria-hidden="true">Close</button>
                  </div>
              </div>
          </div>
      </div>

      <div class="hyperlink-tooltip" data-toggle="tooltip" title="" style="display:none;"></div>

      <!--vendor-->
      <script type="text/javascript" src="../../../vendor/jquery/jquery.min.js"></script>
      <script type="text/javascript" src="../../../vendor/jquery.browser/dist/jquery.browser.min.js"></script>
      <script type="text/javascript" src="../../../vendor/socketio/socket.io.min.js"></script>
      <script type="text/javascript" src="../../../vendor/xregexp/xregexp-all-min.js"></script>

      <script src="../../../vendor/requirejs/require.js"></script>
      <script>
          require.config({
              baseUrl: '../../'
          });
      </script>

      <script type="text/javascript" src="../../../../sdkjs/develop/sdkjs/visio/scripts.js"></script>
      <script>
          window.sdk_scripts.forEach(function(item){
              document.write('<script type="text/javascript" src="' + item + '"><\/script>');
          });
      </script>

      <!--application-->
      <script type="text/javascript" src="../../common/locale.js"></script>
      <script type="text/javascript" src="../../common/Gateway.js"></script>
      <script type="text/javascript" src="../../common/Analytics.js"></script>
      <script type="text/javascript" src="../../common/main/lib/mods/dropdown.js"></script>
      <script type="text/javascript" src="../../common/main/lib/mods/modal.js"></script>
      <script type="text/javascript" src="../../common/main/lib/mods/tooltip.js"></script>
      <script type="text/javascript" src="../../common/embed/lib/util/LocalStorage.js"></script>
      <script type="text/javascript" src="../../common/embed/lib/util/utils.js"></script>
      <script type="text/javascript" src="../../common/embed/lib/view/LoadMask.js"></script>
      <script type="text/javascript" src="../../common/embed/lib/view/modals.js"></script>
      <script type="text/javascript" src="../../common/embed/lib/controller/modals.js"></script>
      <!--<script type="text/javascript" src="../../common/embed/lib/view/SearchBar.js"></script>-->
      <!--<script type="text/javascript" src="js/SearchBar.js"></script>-->
      <script type="text/javascript" src="js/ApplicationView.js"></script>
      <script type="text/javascript" src="js/ApplicationController.js"></script>
      <script type="text/javascript" src="js/application.js"></script>
      <script type="text/javascript">
          var isBrowserSupported = function() {
              return  ($.browser.msie     && parseFloat($.browser.version) > 9)     ||
                      ($.browser.chrome   && parseFloat($.browser.version) > 7)     ||
                      ($.browser.safari   && parseFloat($.browser.version) > 4)     ||
                      ($.browser.opera    && parseFloat($.browser.version) > 10.4)  ||
                      ($.browser.webkit   && parseFloat($.browser.version) > 534.53)  ||
                      ($.browser.mozilla  && parseFloat($.browser.version) > 3.9);
          };

          if (!isBrowserSupported()){
              document.write(
                  '<div id="id-error-mask" class="errormask">',
                      '<div class="error-body" align="center">',
                          '<div id="id-error-mask-title" class="title">Your browser is not supported.</div>',
                          '<div id="id-error-mask-text">Sorry, ONLYOFFICE Document is currently only supported in the latest versions of the Chrome, Firefox, Safari or Internet Explorer web browsers.</div>',
                      '</div>',
                  '</div>'
              );
          }
      </script>
  </body>
</html>
