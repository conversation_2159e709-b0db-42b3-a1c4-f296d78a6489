{"Common.Controllers.Chat.notcriticalErrorTitle": "Warning", "Common.Controllers.Desktop.hintBtnHome": "Show Main window", "Common.Controllers.Desktop.itemCreateFromTemplate": "Create from template", "Common.Controllers.Plugins.helpMoveMacros": "To start working with macros, switch to the View tab.", "Common.Controllers.Plugins.helpMoveMacrosHeader": "The moved Macros button", "Common.Controllers.Plugins.helpUseMacros": "Find the Macros button here", "Common.Controllers.Plugins.helpUseMacrosHeader": "Updated access to macros", "Common.Controllers.Plugins.textPluginsSuccessfullyInstalled": "Plugins are successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textPluginSuccessfullyInstalled": "<b>{0}</b> is successfully installed. You can access all background plugins here.", "Common.Controllers.Plugins.textRunInstalledPlugins": "Run installed plugins", "Common.Controllers.Plugins.textRunPlugin": "Run plugin", "Common.Translation.textMoreButton": "More", "Common.Translation.tipFileLocked": "Document is locked for editing. You can make changes and save it as local copy later.", "Common.Translation.tipFileReadOnly": "The file is read-only. To keep your changes, save the file with a new name or in a different location.", "Common.Translation.warnFileLocked": "You can't edit this file because it's being edited in another app.", "Common.Translation.warnFileLockedBtnEdit": "Create a copy", "Common.Translation.warnFileLockedBtnView": "Open for viewing", "Common.UI.SearchBar.textFind": "Find", "Common.UI.SearchBar.tipCloseSearch": "Close find", "Common.UI.SearchBar.tipNextResult": "Next result", "Common.UI.SearchBar.tipOpenAdvancedSettings": "Open advanced settings", "Common.UI.SearchBar.tipPreviousResult": "Previous result", "Common.UI.SearchDialog.textHighlight": "Highlight results", "Common.UI.SearchDialog.textMatchCase": "Case sensitive", "Common.UI.SearchDialog.textReplaceDef": "Enter the replacement text", "Common.UI.SearchDialog.textSearchStart": "Enter your text here", "Common.UI.SearchDialog.textTitle": "Find and replace", "Common.UI.SearchDialog.textTitle2": "Find", "Common.UI.SearchDialog.textWholeWords": "Whole words only", "Common.UI.SearchDialog.txtBtnHideReplace": "<PERSON><PERSON>", "Common.UI.SearchDialog.txtBtnReplace": "Replace", "Common.UI.SearchDialog.txtBtnReplaceAll": "Replace all", "Common.UI.SynchronizeTip.textDontShow": "Don't show this message again", "Common.UI.SynchronizeTip.textGotIt": "Got it", "Common.UI.SynchronizeTip.textSynchronize": "The document has been changed by another user.<br>Please click to save your changes and reload the updates.", "Common.UI.Themes.txtThemeClassicLight": "Classic light", "Common.UI.Themes.txtThemeContrastDark": "Contrast Dark", "Common.UI.Themes.txtThemeDark": "Dark", "Common.UI.Themes.txtThemeGray": "<PERSON>", "Common.UI.Themes.txtThemeLight": "Light", "Common.UI.Themes.txtThemeSystem": "Same as system", "Common.UI.Window.cancelButtonText": "Cancel", "Common.UI.Window.closeButtonText": "Close", "Common.UI.Window.noButtonText": "No", "Common.UI.Window.okButtonText": "OK", "Common.UI.Window.textConfirmation": "Confirmation", "Common.UI.Window.textDontShow": "Don't show this message again", "Common.UI.Window.textError": "Error", "Common.UI.Window.textInformation": "Information", "Common.UI.Window.textWarning": "Warning", "Common.UI.Window.yesButtonText": "Yes", "Common.Utils.Metric.txtCm": "cm", "Common.Utils.Metric.txtPt": "pt", "Common.Utils.String.textAlt": "Alt", "Common.Utils.String.textComma": ",", "Common.Utils.String.textCtrl": "Ctrl", "Common.Utils.String.textShift": "Shift", "Common.Views.About.txtAddress": "address: ", "Common.Views.About.txtLicensee": "LICENSEE", "Common.Views.About.txtLicensor": "LICENSOR", "Common.Views.About.txtMail": "email: ", "Common.Views.About.txtPoweredBy": "Powered by", "Common.Views.About.txtTel": "tel.: ", "Common.Views.About.txtVersion": "Version ", "Common.Views.Chat.textChat": "Cha<PERSON>", "Common.Views.Chat.textClosePanel": "Close chat", "Common.Views.Chat.textEnterMessage": "Enter your message here", "Common.Views.Chat.textSend": "Send", "Common.Views.CopyWarningDialog.textDontShow": "Don't show this message again", "Common.Views.CopyWarningDialog.textMsg": "Copy, cut and paste actions using the editor toolbar buttons and context menu actions will be performed within this editor tab only.<br><br>To copy or paste to or from applications outside the editor tab use the following keyboard combinations:", "Common.Views.CopyWarningDialog.textTitle": "Copy, cut and paste actions", "Common.Views.CopyWarningDialog.textToCopy": "for Co<PERSON>", "Common.Views.CopyWarningDialog.textToCut": "for Cut", "Common.Views.CopyWarningDialog.textToPaste": "for Paste", "Common.Views.DocumentAccessDialog.textLoading": "Loading...", "Common.Views.DocumentAccessDialog.textTitle": "Sharing settings", "Common.Views.Header.ariaQuickAccessToolbar": "Quick access toolbar", "Common.Views.Header.labelCoUsersDescr": "Users who are editing the file:", "Common.Views.Header.textAddFavorite": "<PERSON> as favorite", "Common.Views.Header.textAdvSettings": "Advanced settings", "Common.Views.Header.textBack": "Open file location", "Common.Views.Header.textClose": "Close file", "Common.Views.Header.textCompactView": "<PERSON><PERSON>", "Common.Views.Header.textDownload": "Download", "Common.Views.Header.textHideLines": "Hide Rulers", "Common.Views.Header.textHideStatusBar": "Hide Status Bar", "Common.Views.Header.textPrint": "Print", "Common.Views.Header.textReadOnly": "Read only", "Common.Views.Header.textRemoveFavorite": "Remove from Favorites", "Common.Views.Header.textShare": "Share", "Common.Views.Header.textZoom": "Zoom", "Common.Views.Header.tipAccessRights": "Manage document access rights", "Common.Views.Header.tipCustomizeQuickAccessToolbar": "Customize Quick Access Toolbar", "Common.Views.Header.tipDownload": "Download file", "Common.Views.Header.tipGoEdit": "Edit current file", "Common.Views.Header.tipPrint": "Print file", "Common.Views.Header.tipPrintQuick": "Quick print", "Common.Views.Header.tipRedo": "Redo", "Common.Views.Header.tipSave": "Save", "Common.Views.Header.tipSearch": "Find", "Common.Views.Header.tipUndo": "Undo", "Common.Views.Header.tipUsers": "View users", "Common.Views.Header.tipViewSettings": "View settings", "Common.Views.Header.tipViewUsers": "View users and manage document access rights", "Common.Views.Header.txtAccessRights": "Change access rights", "Common.Views.Header.txtRename": "<PERSON><PERSON>", "Common.Views.OpenDialog.closeButtonText": "Close file", "Common.Views.OpenDialog.txtEncoding": "Encoding ", "Common.Views.OpenDialog.txtIncorrectPwd": "Password is incorrect.", "Common.Views.OpenDialog.txtOpenFile": "Enter a password to open the file", "Common.Views.OpenDialog.txtPassword": "Password", "Common.Views.OpenDialog.txtPreview": "Preview", "Common.Views.OpenDialog.txtProtected": "Once you enter the password and open the file, the current password to the file will be reset.", "Common.Views.OpenDialog.txtTitle": "Choose %1 options", "Common.Views.OpenDialog.txtTitleProtected": "Protected file", "Common.Views.PluginDlg.textDock": "Pin plugin", "Common.Views.PluginDlg.textLoading": "Loading", "Common.Views.PluginPanel.textClosePanel": "Close plugin", "Common.Views.PluginPanel.textUndock": "Unpin plugin", "Common.Views.PluginPanel.textHidePanel": "Collapse plugin", "Common.Views.PluginPanel.textLoading": "Loading", "Common.Views.Plugins.groupCaption": "Plugins", "Common.Views.Plugins.strPlugins": "Plugins", "Common.Views.Plugins.textBackgroundPlugins": "Background plugins", "Common.Views.Plugins.textClosePanel": "Close plugin", "Common.Views.Plugins.textLoading": "Loading", "Common.Views.Plugins.textSettings": "Settings", "Common.Views.Plugins.textStart": "Start", "Common.Views.Plugins.textStop": "Stop", "Common.Views.Plugins.textTheListOfBackgroundPlugins": "The list of background plugins", "Common.Views.RecentFiles.txtOpenRecent": "Open Recent", "Common.Views.RenameDialog.textName": "File name", "Common.Views.RenameDialog.txtInvalidName": "The file name cannot contain any of the following characters: ", "Common.Views.SaveAsDlg.textLoading": "Loading", "Common.Views.SaveAsDlg.textTitle": "Folder for save", "Common.Views.SearchPanel.textCaseSensitive": "Case sensitive", "Common.Views.SearchPanel.textCloseSearch": "Close find", "Common.Views.SearchPanel.textContentChanged": "Document changed.", "Common.Views.SearchPanel.textFind": "Find", "Common.Views.SearchPanel.textFindAndReplace": "Find and replace", "Common.Views.SearchPanel.textItemsSuccessfullyReplaced": "{0} items successfully replaced.", "Common.Views.SearchPanel.textMatchUsingRegExp": "Match using regular expressions", "Common.Views.SearchPanel.textNoMatches": "No matches", "Common.Views.SearchPanel.textNoSearchResults": "No search results", "Common.Views.SearchPanel.textPartOfItemsNotReplaced": "{0}/{1} items replaced. Remaining {2} items are locked by other users.", "Common.Views.SearchPanel.textReplace": "Replace", "Common.Views.SearchPanel.textReplaceAll": "Replace All", "Common.Views.SearchPanel.textReplaceWith": "Replace with", "Common.Views.SearchPanel.textSearchAgain": "{0}Perform new search{1} for accurate results.", "Common.Views.SearchPanel.textSearchHasStopped": "Search has stopped", "Common.Views.SearchPanel.textSearchResults": "Search results: {0}/{1}", "Common.Views.SearchPanel.textSearchResultsTable": "Search results", "Common.Views.SearchPanel.textTooManyResults": "There are too many results to show here", "Common.Views.SearchPanel.textWholeWords": "Whole words only", "Common.Views.SearchPanel.tipNextResult": "Next result", "Common.Views.SearchPanel.tipPreviousResult": "Previous result", "Common.Views.SelectFileDlg.textLoading": "Loading", "Common.Views.SelectFileDlg.textTitle": "Select data source", "Common.Views.UserNameDialog.textDontShow": "Don't ask me again", "Common.Views.UserNameDialog.textLabel": "Label:", "Common.Views.UserNameDialog.textLabelError": "Label must not be empty.", "VE.Controllers.LeftMenu.newDocumentTitle": "Unnamed document", "VE.Controllers.LeftMenu.notcriticalErrorTitle": "Warning", "VE.Controllers.LeftMenu.requestEditRightsText": "Requesting editing rights...", "VE.Controllers.LeftMenu.textNoTextFound": "The data you have been searching for could not be found. Please adjust your search options.", "VE.Controllers.LeftMenu.textSelectPath": "Enter a new name for saving the file copy", "VE.Controllers.LeftMenu.txtCompatible": "The document will be saved to the new format. It will allow to use all the editor features, but might affect the document layout.<br>Use the 'Compatibility' option of the advanced settings if you want to make the files compatible with older MS Word versions.", "VE.Controllers.LeftMenu.txtUntitled": "Untitled", "VE.Controllers.Main.applyChangesTextText": "Loading the changes...", "VE.Controllers.Main.applyChangesTitleText": "Loading the changes", "VE.Controllers.Main.convertationTimeoutText": "Conversion timeout exceeded.", "VE.Controllers.Main.criticalErrorExtText": "Press \"OK\" to return to document list.", "VE.Controllers.Main.criticalErrorTitle": "Error", "VE.Controllers.Main.downloadErrorText": "Download failed.", "VE.Controllers.Main.downloadMergeText": "Downloading...", "VE.Controllers.Main.downloadMergeTitle": "Downloading", "VE.Controllers.Main.downloadTextText": "Downloading document...", "VE.Controllers.Main.downloadTitleText": "Downloading Document", "VE.Controllers.Main.errorAccessDeny": "You are trying to perform an action you do not have rights for.<br>Please contact your Document Server administrator.", "VE.Controllers.Main.errorBadImageUrl": "Image URL is incorrect", "VE.Controllers.Main.errorCannotPasteImg": "We can't paste this image from the Clipboard, but you can save it to your device and \ninsert it from there, or you can copy the image without text and paste it into the document.", "VE.Controllers.Main.errorCoAuthoringDisconnect": "Server connection lost. The document cannot be edited right now.", "VE.Controllers.Main.errorConnectToServer": "The document could not be saved. Please check connection settings or contact your administrator.<br>When you click the 'OK' button, you will be prompted to download the document.", "VE.Controllers.Main.errorDatabaseConnection": "External error.<br>Database connection error. Please contact support in case the error persists.", "VE.Controllers.Main.errorDataEncrypted": "Encrypted changes have been received, they cannot be deciphered.", "VE.Controllers.Main.errorDataRange": "Incorrect data range.", "VE.Controllers.Main.errorDefaultMessage": "Error code: %1", "VE.Controllers.Main.errorDirectUrl": "Please verify the link to the document.<br>This link must be a direct link to the file for downloading.", "VE.Controllers.Main.errorEditingDownloadas": "An error occurred during the work with the document.<br>Use the 'Download as' option to save the file backup copy to a drive.", "VE.Controllers.Main.errorEditingSaveas": "An error occurred during the work with the document.<br>Use the 'Save as...' option to save the file backup copy to a drive.", "VE.Controllers.Main.errorEmailClient": "No email client could be found.", "VE.Controllers.Main.errorFilePassProtect": "The file is password protected and cannot be opened.", "VE.Controllers.Main.errorFileSizeExceed": "The file size exceeds the limitation set for your server.<br>Please contact your Document Server administrator for details.", "VE.Controllers.Main.errorForceSave": "An error occurred while saving the file. Please use the 'Download as' option to save the file to a drive or try again later.", "VE.Controllers.Main.errorInconsistentExt": "An error has occurred while opening the file.<br>The file content does not match the file extension.", "VE.Controllers.Main.errorInconsistentExtDocx": "An error has occurred while opening the file.<br>The file content corresponds to text documents (e.g. docx), but the file has the inconsistent extension: %1.", "VE.Controllers.Main.errorInconsistentExtPdf": "An error has occurred while opening the file.<br>The file content corresponds to one of the following formats: pdf/djvu/xps/oxps, but the file has the inconsistent extension: %1.", "VE.Controllers.Main.errorInconsistentExtPptx": "An error has occurred while opening the file.<br>The file content corresponds to presentations (e.g. pptx), but the file has the inconsistent extension: %1.", "VE.Controllers.Main.errorInconsistentExtXlsx": "An error has occurred while opening the file.<br>The file content corresponds to spreadsheets (e.g. xlsx), but the file has the inconsistent extension: %1.", "VE.Controllers.Main.errorKeyEncrypt": "Unknown key descriptor", "VE.Controllers.Main.errorKeyExpire": "Key descriptor expired", "VE.Controllers.Main.errorLoadingFont": "Fonts are not loaded.<br>Please contact your Document Server administrator.", "VE.Controllers.Main.errorPasswordIsNotCorrect": "The password you supplied is not correct.<br>Verify that the CAPS LOCK key is off and be sure to use the correct capitalization.", "VE.Controllers.Main.errorProcessSaveResult": "Saving failed.", "VE.Controllers.Main.errorServerVersion": "The editor version has been updated. The page will be reloaded to apply the changes.", "VE.Controllers.Main.errorSessionAbsolute": "The document editing session has expired. Please reload the page.", "VE.Controllers.Main.errorSessionIdle": "The document has not been edited for quite a long time. Please reload the page.", "VE.Controllers.Main.errorSessionToken": "The connection to the server has been interrupted. Please reload the page.", "VE.Controllers.Main.errorSetPassword": "Password could not be set.", "VE.Controllers.Main.errorToken": "The document security token is not correctly formed.<br>Please contact your Document Server administrator.", "VE.Controllers.Main.errorTokenExpire": "The document security token has expired.<br>Please contact your Document Server administrator.", "VE.Controllers.Main.errorUpdateVersion": "The file version has been changed. The page will be reloaded.", "VE.Controllers.Main.errorUpdateVersionOnDisconnect": "Connection has been restored, and the file version has been changed.<br>Before you can continue working, you need to download the file or copy its content to make sure nothing is lost, and then reload this page.", "VE.Controllers.Main.errorUserDrop": "The file cannot be accessed right now.", "VE.Controllers.Main.errorUsersExceed": "The number of users allowed by the pricing plan was exceeded", "VE.Controllers.Main.errorViewerDisconnect": "Connection is lost. You can still view the document,<br>but will not be able to download or print it until the connection is restored and page is reloaded.", "VE.Controllers.Main.leavePageText": "You have unsaved changes in this document. Click \"Stay on This Page\", then \"Save\" to save them. Click \"Leave This Page\" to discard all the unsaved changes.", "VE.Controllers.Main.leavePageTextOnClose": "All unsaved changes in this document will be lost.<br> Click \"Cancel\" then \"Save\" to save them. Click \"OK\" to discard all the unsaved changes.", "VE.Controllers.Main.loadFontsTextText": "Loading data...", "VE.Controllers.Main.loadFontsTitleText": "Loading data", "VE.Controllers.Main.loadFontTextText": "Loading data...", "VE.Controllers.Main.loadFontTitleText": "Loading data", "VE.Controllers.Main.loadImagesTextText": "Loading images...", "VE.Controllers.Main.loadImagesTitleText": "Loading Images", "VE.Controllers.Main.loadImageTextText": "Loading image...", "VE.Controllers.Main.loadImageTitleText": "Loading Image", "VE.Controllers.Main.loadingDocumentTextText": "Loading document...", "VE.Controllers.Main.loadingDocumentTitleText": "Loading document", "VE.Controllers.Main.notcriticalErrorTitle": "Warning", "VE.Controllers.Main.openErrorText": "An error has occurred while opening the file.", "VE.Controllers.Main.openTextText": "Opening document...", "VE.Controllers.Main.openTitleText": "Opening Document", "VE.Controllers.Main.printTextText": "Printing document...", "VE.Controllers.Main.printTitleText": "Printing Document", "VE.Controllers.Main.reloadButtonText": "Reload Page", "VE.Controllers.Main.requestEditFailedMessageText": "Someone is editing this document right now. Please try again later.", "VE.Controllers.Main.requestEditFailedTitleText": "Access denied", "VE.Controllers.Main.saveErrorText": "An error has occurred while saving the file.", "VE.Controllers.Main.saveErrorTextDesktop": "This file cannot be saved or created.<br>Possible reasons are: <br>1. The file is read-only. <br>2. The file is being edited by other users. <br>3. The disk is full or corrupted.", "VE.Controllers.Main.saveTextText": "Saving document...", "VE.Controllers.Main.saveTitleText": "Saving Document", "VE.Controllers.Main.scriptLoadError": "The connection is too slow, some of the components could not be loaded. Please reload the page.", "VE.Controllers.Main.textAnonymous": "Anonymous", "VE.Controllers.Main.textAnyone": "Anyone", "VE.Controllers.Main.textBuyNow": "Visit website", "VE.Controllers.Main.textChangesSaved": "All changes saved", "VE.Controllers.Main.textClose": "Close", "VE.Controllers.Main.textCloseTip": "Click to close the tip", "VE.Controllers.Main.textConnectionLost": "Trying to connect. Please check connection settings.", "VE.Controllers.Main.textContactUs": "Contact sales", "VE.Controllers.Main.textContinue": "Continue", "VE.Controllers.Main.textCustomLoader": "Please note that according to the terms of the license you are not entitled to change the loader.<br>Please contact our Sales Department to get a quote.", "VE.Controllers.Main.textDisconnect": "Connection is lost", "VE.Controllers.Main.textGuest": "Guest", "VE.Controllers.Main.textLearnMore": "Learn more", "VE.Controllers.Main.textLoadingDocument": "Loading document", "VE.Controllers.Main.textLongName": "Enter a name that is less than 128 characters.", "VE.Controllers.Main.textNoLicenseTitle": "License limit reached", "VE.Controllers.Main.textPaidFeature": "Paid feature", "VE.Controllers.Main.textReconnect": "Connection is restored", "VE.Controllers.Main.textRemember": "Remember my choice for all files", "VE.Controllers.Main.textRenameError": "User name must not be empty.", "VE.Controllers.Main.textRenameLabel": "Enter a name to be used for collaboration", "VE.Controllers.Main.textShape": "<PERSON><PERSON><PERSON>", "VE.Controllers.Main.textStrict": "Strict mode", "VE.Controllers.Main.textText": "Text", "VE.Controllers.Main.textTryQuickPrint": "You have selected Quick print: the entire document will be printed on the last selected or default printer.<br>Do you want to continue?", "VE.Controllers.Main.textUpdateVersion": "The document cannot be edited right now.<br>Trying to update file, please wait...", "VE.Controllers.Main.textUpdating": "Updating", "VE.Controllers.Main.titleLicenseExp": "License expired", "VE.Controllers.Main.titleLicenseNotActive": "License not active", "VE.Controllers.Main.titleServerVersion": "Editor updated", "VE.Controllers.Main.titleUpdateVersion": "Version changed", "VE.Controllers.Main.txtSaveCopyAsComplete": "The file copy was successfully saved", "VE.Controllers.Main.txtSecurityWarningLink": "This document is trying to connect to {0}.<br>If you trust this site, press \"OK\" while holding down the ctrl key.", "VE.Controllers.Main.txtSecurityWarningOpenFile": "This document is trying to open file dialog, press \"OK\" to open.", "VE.Controllers.Main.unknownErrorText": "Unknown error.", "VE.Controllers.Main.unsupportedBrowserErrorText": "Your browser is not supported.", "VE.Controllers.Main.uploadDocExtMessage": "Unknown document format.", "VE.Controllers.Main.uploadDocFileCountMessage": "No documents uploaded.", "VE.Controllers.Main.uploadDocSizeMessage": "Maximum document size limit exceeded.", "VE.Controllers.Main.uploadImageExtMessage": "Unknown image format.", "VE.Controllers.Main.uploadImageFileCountMessage": "No images uploaded.", "VE.Controllers.Main.uploadImageSizeMessage": "The image is too big. The maximum size is 25 MB.", "VE.Controllers.Main.uploadImageTextText": "Uploading image...", "VE.Controllers.Main.uploadImageTitleText": "Uploading Image", "VE.Controllers.Main.waitText": "Please, wait...", "VE.Controllers.Main.warnBrowserIE9": "The application has low capabilities on IE9. Use IE10 or higher", "VE.Controllers.Main.warnBrowserZoom": "Your browser current zoom setting is not fully supported. Please reset to the default zoom by pressing Ctrl+0.", "VE.Controllers.Main.warnLicenseAnonymous": "Access denied for anonymous users.<br>This document will be opened for viewing only.", "VE.Controllers.Main.warnLicenseBefore": "License not active.<br>Please contact your administrator.", "VE.Controllers.Main.warnLicenseExceeded": "You've reached the limit for simultaneous connections to %1 editors. This document will be opened for viewing only.<br>Contact your administrator to learn more.", "VE.Controllers.Main.warnLicenseExp": "Your license has expired.<br>Please update your license and refresh the page.", "VE.Controllers.Main.warnLicenseLimitedNoAccess": "License expired.<br>You have no access to document editing functionality.<br>Please contact your administrator.", "VE.Controllers.Main.warnLicenseLimitedRenewed": "License needs to be renewed.<br>You have a limited access to document editing functionality.<br>Please contact your administrator to get full access", "VE.Controllers.Main.warnLicenseUsersExceeded": "You've reached the user limit for %1 editors. Contact your administrator to learn more.", "VE.Controllers.Main.warnNoLicense": "You've reached the limit for simultaneous connections to %1 editors. This document will be opened for viewing only.<br>Contact %1 sales team for personal upgrade terms.", "VE.Controllers.Main.warnNoLicenseUsers": "You've reached the user limit for %1 editors. Contact %1 sales team for personal upgrade terms.", "VE.Controllers.Main.warnProcessRightsChange": "You have been denied the right to edit the file.", "VE.Controllers.Search.notcriticalErrorTitle": "Warning", "VE.Controllers.Search.textNoTextFound": "The data you have been searching for could not be found. Please adjust your search options.", "VE.Controllers.Search.textReplaceSkipped": "The replacement has been made. {0} occurrences were skipped.", "VE.Controllers.Search.textReplaceSuccess": "Search has been done. {0} occurrences have been replaced", "VE.Controllers.Search.warnReplaceString": "{0} is not a valid special character for the Replace With box.", "VE.Controllers.Statusbar.textDisconnect": "<b>Connection is lost</b><br>Trying to connect. Please check connection settings.", "VE.Controllers.Statusbar.zoomText": "Zoom {0}%", "VE.Controllers.Toolbar.errorAccessDeny": "You are trying to perform an action you do not have rights for.<br>Please contact your Document Server administrator.", "VE.Controllers.Toolbar.notcriticalErrorTitle": "Warning", "VE.Controllers.Toolbar.txtUntitled": "Untitled", "VE.Views.DocumentHolder.guestText": "Guest", "VE.Views.DocumentHolder.textCopy": "Copy", "VE.Views.DocumentHolder.txtPressLink": "Press {0} and click link", "VE.Views.DocumentHolder.txtWarnUrl": "Clicking this link can be harmful to your device and data.<br>Are you sure you want to continue?", "VE.Views.FileMenu.ariaFileMenu": "File menu", "VE.Views.FileMenu.btnBackCaption": "Open File Location", "VE.Views.FileMenu.btnCloseEditor": "Close File", "VE.Views.FileMenu.btnCloseMenuCaption": "Back", "VE.Views.FileMenu.btnCreateNewCaption": "Create New", "VE.Views.FileMenu.btnDownloadCaption": "Download As", "VE.Views.FileMenu.btnExitCaption": "Close", "VE.Views.FileMenu.btnFileOpenCaption": "Open", "VE.Views.FileMenu.btnHelpCaption": "Help", "VE.Views.FileMenu.btnInfoCaption": "Info", "VE.Views.FileMenu.btnPrintCaption": "Print", "VE.Views.FileMenu.btnRecentFilesCaption": "Open Recent", "VE.Views.FileMenu.btnRenameCaption": "<PERSON><PERSON>", "VE.Views.FileMenu.btnReturnCaption": "Back to Document", "VE.Views.FileMenu.btnRightsCaption": "Access Rights", "VE.Views.FileMenu.btnSaveAsCaption": "Save As", "VE.Views.FileMenu.btnSaveCopyAsCaption": "Save Copy As", "VE.Views.FileMenu.btnSettingsCaption": "Advanced Settings", "VE.Views.FileMenu.btnSwitchToMobileCaption": "Switch to Mobile", "VE.Views.FileMenu.textDownload": "Download", "VE.Views.FileMenuPanels.CreateNew.txtBlank": "Blank Document", "VE.Views.FileMenuPanels.CreateNew.txtCreateNew": "Create new", "VE.Views.FileMenuPanels.DocumentInfo.okButtonText": "Apply", "VE.Views.FileMenuPanels.DocumentInfo.txtAddAuthor": "Add Author", "VE.Views.FileMenuPanels.DocumentInfo.txtAddText": "Add Text", "VE.Views.FileMenuPanels.DocumentInfo.txtAppName": "Application", "VE.Views.FileMenuPanels.DocumentInfo.txtAuthor": "Author", "VE.Views.FileMenuPanels.DocumentInfo.txtBtnAccessRights": "Change access rights", "VE.Views.FileMenuPanels.DocumentInfo.txtComment": "Comment", "VE.Views.FileMenuPanels.DocumentInfo.txtCommon": "Common", "VE.Views.FileMenuPanels.DocumentInfo.txtCreated": "Created", "VE.Views.FileMenuPanels.DocumentInfo.txtDocumentInfo": "Document Info", "VE.Views.FileMenuPanels.DocumentInfo.txtLoading": "Loading...", "VE.Views.FileMenuPanels.DocumentInfo.txtModifyBy": "Last modified by", "VE.Views.FileMenuPanels.DocumentInfo.txtModifyDate": "Last modified", "VE.Views.FileMenuPanels.DocumentInfo.txtNo": "No", "VE.Views.FileMenuPanels.DocumentInfo.txtOwner": "Owner", "VE.Views.FileMenuPanels.DocumentInfo.txtPlacement": "Location", "VE.Views.FileMenuPanels.DocumentInfo.txtRights": "Persons who have rights", "VE.Views.FileMenuPanels.DocumentInfo.txtSubject": "Subject", "VE.Views.FileMenuPanels.DocumentInfo.txtTags": "Tags", "VE.Views.FileMenuPanels.DocumentInfo.txtTitle": "Title", "VE.Views.FileMenuPanels.DocumentInfo.txtUploaded": "Uploaded", "VE.Views.FileMenuPanels.DocumentInfo.txtYes": "Yes", "VE.Views.FileMenuPanels.DocumentRights.txtAccessRights": "Access Rights", "VE.Views.FileMenuPanels.DocumentRights.txtBtnAccessRights": "Change access rights", "VE.Views.FileMenuPanels.DocumentRights.txtRights": "Persons who have rights", "VE.Views.FileMenuPanels.Settings.okButtonText": "Apply", "VE.Views.FileMenuPanels.Settings.strFontRender": "Font hinting", "VE.Views.FileMenuPanels.Settings.strTabStyle": "Tab style", "VE.Views.FileMenuPanels.Settings.strTheme": "Interface theme", "VE.Views.FileMenuPanels.Settings.strZoom": "Default Zoom Value", "VE.Views.FileMenuPanels.Settings.textDisabled": "Disabled", "VE.Views.FileMenuPanels.Settings.textFill": "Fill", "VE.Views.FileMenuPanels.Settings.textLine": "Line", "VE.Views.FileMenuPanels.Settings.txtAdvancedSettings": "Advanced Settings", "VE.Views.FileMenuPanels.Settings.txtAppearance": "Appearance", "VE.Views.FileMenuPanels.Settings.txtCacheMode": "Default cache mode", "VE.Views.FileMenuPanels.Settings.txtFitPage": "Fit to <PERSON>", "VE.Views.FileMenuPanels.Settings.txtFitWidth": "Fit to Width", "VE.Views.FileMenuPanels.Settings.txtLastUsed": "Last used", "VE.Views.FileMenuPanels.Settings.txtMac": "as OS X", "VE.Views.FileMenuPanels.Settings.txtNative": "Native", "VE.Views.FileMenuPanels.Settings.txtQuickPrintTip": "The document will be printed on the last selected or default printer", "VE.Views.FileMenuPanels.Settings.txtScreenReader": "Turn on screen reader support", "VE.Views.FileMenuPanels.Settings.txtTabBack": "Use toolbar color as tabs background", "VE.Views.FileMenuPanels.Settings.txtUseAltKey": "Use Alt key to navigate the user interface using the keyboard", "VE.Views.FileMenuPanels.Settings.txtUseOptionKey": "Use Option key to navigate the user interface using the keyboard", "VE.Views.FileMenuPanels.Settings.txtWin": "as Windows", "VE.Views.FileMenuPanels.Settings.txtWorkspace": "Workspace", "VE.Views.FileMenuPanels.ViewSaveAs.textDownloadAs": "Download as", "VE.Views.FileMenuPanels.ViewSaveCopy.textSaveCopyAs": "Save Copy as", "VE.Views.LeftMenu.ariaLeftMenu": "Left menu", "VE.Views.LeftMenu.tipAbout": "About", "VE.Views.LeftMenu.tipChat": "Cha<PERSON>", "VE.Views.LeftMenu.tipPages": "Pages", "VE.Views.LeftMenu.tipPlugins": "Plugins", "VE.Views.LeftMenu.tipSearch": "Find", "VE.Views.LeftMenu.tipSupport": "Feedback & Support", "VE.Views.LeftMenu.txtDeveloper": "DEVELOPER MODE", "VE.Views.LeftMenu.txtEditor": "Diagram Viewer", "VE.Views.LeftMenu.txtLimit": "Limit Access", "VE.Views.LeftMenu.txtTrial": "TRIAL MODE", "VE.Views.LeftMenu.txtTrialDev": "Trial Developer Mode", "VE.Views.Statusbar.sheetIndexText": "Page {0} of {1}", "VE.Views.Statusbar.tipFitPage": "Fit to page", "VE.Views.Statusbar.tipFitWidth": "Fit to width", "VE.Views.Statusbar.tipListOfSheets": "List of pages", "VE.Views.Statusbar.tipNext": "Next page", "VE.Views.Statusbar.tipPrev": "Previous page", "VE.Views.Statusbar.tipZoomFactor": "Zoom", "VE.Views.Statusbar.tipZoomIn": "Zoom in", "VE.Views.Statusbar.tipZoomOut": "Zoom out", "VE.Views.Statusbar.txtPage": "Page", "VE.Views.Toolbar.textTabFile": "File", "VE.Views.Toolbar.textTabView": "View", "VE.Views.ViewTab.textAlwaysShowToolbar": "Always Show Toolbar", "VE.Views.ViewTab.textFill": "Fill", "VE.Views.ViewTab.textFitPage": "<PERSON><PERSON> To <PERSON>", "VE.Views.ViewTab.textFitWidth": "Fit To <PERSON>th", "VE.Views.ViewTab.textInterfaceTheme": "Interface Theme", "VE.Views.ViewTab.textLeftMenu": "Left Panel", "VE.Views.ViewTab.textLine": "Line", "VE.Views.ViewTab.textRightMenu": "Right Panel", "VE.Views.ViewTab.textStatusBar": "Status Bar", "VE.Views.ViewTab.textTabStyle": "Tab style", "VE.Views.ViewTab.textZoom": "Zoom", "VE.Views.ViewTab.tipFitPage": "Fit to page", "VE.Views.ViewTab.tipFitWidth": "Fit to width", "VE.Views.ViewTab.tipInterfaceTheme": "Interface theme"}