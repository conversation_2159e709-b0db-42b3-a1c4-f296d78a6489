var editor=void 0,navigator={userAgent:"chrome"};window.location={},window.location.protocol="",window.location.host="",window.location.href="",window.location.pathname="",window.XMLHttpRequest=function(){},window.NATIVE_EDITOR_ENJINE=!0,window.NATIVE_EDITOR_ENJINE_SYNC_RECALC=!0;var document={},Asc={},AscFonts={},AscCommon={},AscFormat={},AscDFH={},AscCH={},AscCommonExcel={},AscCommonWord={},AscMath={},AscCommonSlide={},AscBuilder={},AscWord={},AscJsonConverter={};function Image(){this.src="",this.onload=function(){},this.onerror=function(){}}function _image_data(){this.data=null,this.length=0}function native_pattern_fill(){}function native_gradient_fill(){}function native_context2d(t){this.canvas=t,this.globalAlpha=0,this.globalCompositeOperation="",this.fillStyle="#000000",this.strokeStyle="#000000",this.lineWidth=0,this.lineCap=0,this.lineJoin=0,this.miterLimit=0,this.shadowOffsetX=0,this.shadowOffsetY=0,this.shadowBlur=0,this.shadowColor=0,this.font="",this.textAlign=0,this.textBaseline=0}function native_canvas(){this.id="",this.width=300,this.height=150,this.nodeType=1}native_pattern_fill.prototype={setTransform:function(t){}},native_gradient_fill.prototype={addColorStop:function(t,n){}},native_context2d.prototype={save:function(){},restore:function(){},scale:function(t,n){},rotate:function(t){},translate:function(t,n){},transform:function(t,n,e,o,i,c){},setTransform:function(t,n,e,o,i,c){},createLinearGradient:function(t,n,e,o){},createRadialGradient:function(t,n,e,o,i,c){},createPattern:function(t,n){},clearRect:function(t,n,e,o){},fillRect:function(t,n,e,o){},strokeRect:function(t,n,e,o){},beginPath:function(){},closePath:function(){},moveTo:function(t,n){},lineTo:function(t,n){},quadraticCurveTo:function(t,n,e,o){},bezierCurveTo:function(t,n,e,o,i,c){},arcTo:function(t,n,e,o,i){},rect:function(t,n,e,o){},arc:function(t,n,e,o,i,c){},fill:function(){},stroke:function(){},clip:function(){},isPointInPath:function(t,n){},drawFocusRing:function(t,n,e,o){},fillText:function(t,n,e,o){},strokeText:function(t,n,e,o){},measureText:function(t){},drawImage:function(t,n,e,o,i,c,l,u,a){},createImageData:function(t,n){var e=new _image_data;return e.length=t*n*4,e.data="undefined"!=typeof Uint8Array?new Uint8Array(e.length):new Array(e.length),e},getImageData:function(t,n,e,o){},putImageData:function(t,n,e,o,i,c,l){}},native_canvas.prototype={getContext:function(t){return"2d"==t?new native_context2d(this):null},toDataUrl:function(t){return""},addEventListener:function(){},attr:function(){}};var _null_object={};function _return_empty_html_element(){return _null_object}function GetNativeEngine(){return window.native}_null_object.length=0,_null_object.nodeType=1,_null_object.offsetWidth=1,_null_object.offsetHeight=1,_null_object.clientWidth=1,_null_object.clientHeight=1,_null_object.scrollWidth=1,_null_object.scrollHeight=1,_null_object.style={},_null_object.documentElement=_null_object,_null_object.body=_null_object,_null_object.ownerDocument=_null_object,_null_object.defaultView=_null_object,_null_object.addEventListener=function(){},_null_object.setAttribute=function(){},_null_object.getElementsByTagName=function(){return[]},_null_object.appendChild=function(){},_null_object.removeChild=function(){},_null_object.insertBefore=function(){},_null_object.childNodes=[],_null_object.parent=_null_object,_null_object.parentNode=_null_object,_null_object.find=function(){return this},_null_object.appendTo=function(){return this},_null_object.css=function(){return this},_null_object.width=function(){return 0},_null_object.height=function(){return 0},_null_object.attr=function(){return this},_null_object.prop=function(){return this},_null_object.val=function(){return this},_null_object.remove=function(){},_null_object.getComputedStyle=function(){return null},_null_object.getContext=function(t){return"2d"==t?new native_context2d(this):null},_null_object.getBoundingClientRect=function(){return{left:0,top:0,right:0,bottom:0}},document.createElement=function(t){return t&&t.toLowerCase&&"canvas"==t.toLowerCase()?new native_canvas:_null_object},document.createDocumentFragment=_return_empty_html_element,document.getElementsByTagName=function(t){return"head"==t?[_null_object]:[]},document.insertBefore=function(){},document.appendChild=function(){},document.removeChild=function(){},document.getElementById=function(){return _null_object},document.createComment=function(){},document.documentElement=_null_object,document.body=_null_object,window.native=native;var Api=null;function NativeOpenFileData(t,n,e,o){window.NATIVE_DOCUMENT_TYPE=window.native.GetEditorType(),Api=null,o&&o.printOptions&&o.printOptions.retina&&(AscBrowser.isRetina=!0);var i={};o&&void 0!==o.translate&&(i.translate=o.translate),"presentation"===window.NATIVE_DOCUMENT_TYPE||"document"===window.NATIVE_DOCUMENT_TYPE?(Api=new window.Asc.asc_docs_api(i),o&&o.documentLayout&&void 0!==o.documentLayout.openedAt&&Api.setOpenedAt(o.documentLayout.openedAt)):Api=new window.Asc.spreadsheet_api(i),o&&void 0!==o.locale&&Api.asc_setLocale(o.locale),"presentation"===window.NATIVE_DOCUMENT_TYPE||"document"===window.NATIVE_DOCUMENT_TYPE?Api.asc_nativeOpenFile(t,n):Api.asc_nativeOpenFile(t,n,void 0,e)}window.devicePixelRatio=1,window.native&&window.native.GetDevicePixelRatio&&(window.devicePixelRatio=window.native.GetDevicePixelRatio());var clearTimeout=window.clearTimeout=function(){},setTimeout=window.setTimeout=function(){},clearInterval=window.clearInterval=function(){},setInterval=window.setInterval=function(){},console={log:function(t){window.native.ConsoleLog(t)},time:function(t){},timeEnd:function(t){},warn:function(){}},performance=window.performance=function(){var t=Date.now();return{now:function(){return Date.now()-t}}}();