<!DOCTYPE html>
<html style="width:100%; height:100%;overflow: hidden;">
<head>
    <title>演示者视图</title>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=IE8"/>
    <meta name="description" content="" />
    <meta name="keywords" content="" />

    <!-- splash -->        

    <style>
        .loadmask {
            left: 0;
            top: 0;
            position: absolute;
            height: 100%;
            width: 100%;
            overflow: hidden;
            border: none;
            background: #f0f0f0;
            background: var(--canvas-background, #f0f0f0);
            z-index: 10000;
        }

        .loadmask > .brendpanel {
            width: 100%;
            height: 56px;
            background: #B7472A;
            background: var(--toolbar-header-presentation, #B7472A);
        }

        .loadmask > .brendpanel > div {
            display: flex;
            align-items: center;
        }

        .loadmask > .brendpanel .spacer {
            margin-left: auto;
        }

        .loadmask > .brendpanel .circle {
            vertical-align: middle;
            width: 20px;
            height: 20px;
            border-radius: 12px;
            margin: 4px 10px;
            background: rgba(255, 255, 255, 0.2);
        }

        .loadmask > .brendpanel .rect {
            vertical-align: middle;
            width: 50px;
            height: 12px;
            border-radius: 3px;
            margin: 0 10px;
            background: rgba(255, 255, 255, 0.2);
        }

        .loadmask > .placeholder {
            display: flex;
            flex-direction: column;
            min-height: 100%;
            margin: 0 100px;
        }

        .loadmask > .placeholder .slide-h {
            display: flex;
            flex-direction: column;
            justify-content: center;
            flex-grow: 1;
            max-width: 1350px;
            width: 100%;
        }
        .loadmask > .placeholder .slide-v {
            display: flex;
            position: relative;
            flex-direction: column;
            padding-bottom: 80%;
        }

        .loadmask > .placeholder .slide-container {
            position: absolute;
            height: 100%;
            width: 100%;
            background: #fff;
            background: var(--canvas-content-background, #fff);
            border: 1px solid #ccc;
            border: var(--scaled-one-px-value, 1px) solid var(--canvas-page-border, #ccc);

            -webkit-animation: flickerAnimation 2s infinite ease-in-out;
            -moz-animation: flickerAnimation 2s infinite ease-in-out;
            -o-animation: flickerAnimation 2s infinite ease-in-out;
            animation: flickerAnimation 2s infinite ease-in-out;
        }

        .loadmask > .placeholder .slide-container > .line {
            height: 20%;
            margin: 0 120px;
            border-radius: 6px;
            background: #f5f5f5;
        }

        .loadmask > .placeholder .slide-container > .line.empty {
            background: transparent;
        }

        .loadmask > .placeholder .slide-container > .line:nth-child(1) {
            height: 30%;
            margin: 80px 80px 0;
        }

        @keyframes flickerAnimation {
            0%   { opacity:1; }
            50%  { opacity:0.3; }
            100% { opacity:1; }
        }
        @-o-keyframes flickerAnimation{
            0%   { opacity:1; }
            50%  { opacity:0.3; }
            100% { opacity:1; }
        }
        @-moz-keyframes flickerAnimation{
            0%   { opacity:1; }
            50%  { opacity:0.3; }
            100% { opacity:1; }
        }
        @-webkit-keyframes flickerAnimation{
            0%   { opacity:1; }
            50%  { opacity:0.3; }
            100% { opacity:1; }
        }
    </style>

    <style>
        body {
            margin: 0;
        }
    </style>
    <script src="../../../../sdkjs/common/AllFonts.js"></script>
    <script src="../../../../sdkjs/slide/sdk-all-min.js"></script>
    <script src="../../../apps/common/main/lib/util/min-log.js"></script>
</head>
<body>
    <div id="loading-mask" class="loadmask"><div class="placeholder"><div class="slide-h"><div class="slide-v"><div class="slide-container"><div class="line"></div><div class="line empty"></div><div class="line"></div></div></div></div></div></div>
    <div id="viewport"></div>

    <div id="editor_sdk" style="position: absolute;width: 100%;height: 100%;"></div>

    <script data-main="app.reporter" src="../../../vendor/requirejs/require.js"></script>

</body>
</html>