{"name": "onlyoffice-web-local", "version": "1.0.0", "private": true, "type": "module", "keywords": ["onlyoffice", "vue3", "element-plus", "wasm", "wasm-pack", "typescript", "pinia", "vite", "esm"], "homepage": "https://sweetwisdom.github.io/onlyoffice-web-local/", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test:e2e": "playwright test", "build-only": "vite build", "type-check": "vue-tsc --build", "format": "prettier --write src/"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "element-plus": "^2.9.11", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@arco-design/web-vue": "^2.57.0", "@playwright/test": "^1.51.1", "@tsconfig/node22": "^22.0.1", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.3", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/tsconfig": "^0.7.0", "less": "^4.3.0", "npm-run-all2": "^7.0.2", "prettier": "3.5.3", "typescript": "~5.8.0", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.5.0", "vite": "^6.2.4", "vite-plugin-compression": "^0.5.1", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}, "engines": {"node": "^18.18.0 || ^20.9.0 || >=22.0.0", "pnpm": ">=9"}}