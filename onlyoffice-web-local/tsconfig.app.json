{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["element-plus/global"], "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["**/*.ts", "**/*.tsx", "**/*.vue", "tests/**/*.ts", "tests/**/*.tsx"], "exclude": ["node_modules", "public", "web-apps", "dist"]}